import { Link } from 'react-router-dom';

const Home = () => {
  return (
    <div className="min-h-screen text-white" style={{ backgroundColor: '#030412' }}>
      {/* Hero Section */}
      <section className="px-5 sm:px-10 lg:px-15 py-20 min-h-screen mt-20 md:mt-30">
        <div className="text-center">
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            Welcome to <span style={{ color: '#33c2cc' }}>Langit Biru</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto" style={{ color: '#a3a3a3' }}>
            Creating extraordinary events that leave lasting impressions.
            Your vision, our expertise, unforgettable experiences.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="px-8 py-3 rounded-lg text-lg font-semibold transition-colors"
              style={{ backgroundColor: '#33c2cc', color: '#030412' }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#57db96'}
              onMouseLeave={(e) => e.target.style.backgroundColor = '#33c2cc'}
            >
              Get Started
            </Link>
            <Link
              to="/work"
              className="border-2 px-8 py-3 rounded-lg text-lg font-semibold transition-colors"
              style={{ borderColor: '#33c2cc', color: '#33c2cc' }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#33c2cc';
                e.target.style.color = '#030412';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent';
                e.target.style.color = '#33c2cc';
              }}
            >
              View Our Work
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="px-5 sm:px-10 lg:px-15 py-16">
        <div className="grid md:grid-cols-3 gap-8">
          <div className="p-6 rounded-2xl hover:-translate-y-1 duration-200" style={{ background: 'linear-gradient(to bottom, #282b4b, #1f1e39)' }}>
            <div className="w-12 h-12 rounded-lg flex items-center justify-center mb-4" style={{ backgroundColor: 'rgba(51, 194, 204, 0.2)' }}>
              <svg className="w-6 h-6" style={{ color: '#33c2cc' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-3">Professional Planning</h3>
            <p style={{ color: '#a3a3a3' }}>Expert event planning with attention to every detail, ensuring your event runs smoothly from start to finish.</p>
          </div>

          <div className="p-6 rounded-2xl hover:-translate-y-1 duration-200" style={{ background: 'linear-gradient(to bottom, #282b4b, #1f1e39)' }}>
            <div className="w-12 h-12 rounded-lg flex items-center justify-center mb-4" style={{ backgroundColor: 'rgba(87, 219, 150, 0.2)' }}>
              <svg className="w-6 h-6" style={{ color: '#57db96' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-3">Creative Solutions</h3>
            <p style={{ color: '#a3a3a3' }}>Innovative and creative approaches to make your event unique and memorable for all attendees.</p>
          </div>

          <div className="p-6 rounded-2xl hover:-translate-y-1 duration-200" style={{ background: 'linear-gradient(to bottom, #282b4b, #1f1e39)' }}>
            <div className="w-12 h-12 rounded-lg flex items-center justify-center mb-4" style={{ backgroundColor: 'rgba(122, 87, 219, 0.2)' }}>
              <svg className="w-6 h-6" style={{ color: '#7a57db' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-3">Fast Execution</h3>
            <p style={{ color: '#a3a3a3' }}>Quick turnaround times without compromising quality, delivering exceptional results on schedule.</p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
