import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Services', path: '/services' },
    { name: 'About', path: '/about' },
    { name: 'Work', path: '/work' },
    { name: 'Blog', path: '/blog' },
    { name: 'Contact', path: '/contact' }
  ];

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <nav className="sticky top-0 z-50" style={{ backgroundColor: '#030412', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
      <div className="px-5 sm:px-10 lg:px-15">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#33c2cc' }}>
              <span className="font-bold text-xl" style={{ color: '#030412' }}>LB</span>
            </div>
            <span className="text-2xl font-bold text-white">Langit Biru</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className={`text-lg font-medium transition-colors pb-1 ${
                  isActive(item.path) ? 'border-b-2' : ''
                }`}
                style={{
                  color: isActive(item.path) ? '#33c2cc' : '#a3a3a3',
                  borderColor: isActive(item.path) ? '#33c2cc' : 'transparent'
                }}
                onMouseEnter={(e) => e.target.style.color = '#ffffff'}
                onMouseLeave={(e) => e.target.style.color = isActive(item.path) ? '#33c2cc' : '#a3a3a3'}
              >
                {item.name}
              </Link>
            ))}
            <Link
              to="/contact"
              className="px-6 py-2 rounded-lg font-semibold transition-colors"
              style={{ backgroundColor: '#33c2cc', color: '#030412' }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#57db96'}
              onMouseLeave={(e) => e.target.style.backgroundColor = '#33c2cc'}
            >
              Get Quote
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden flex items-center px-3 py-2 border rounded transition-colors"
            style={{ color: '#a3a3a3', borderColor: 'rgba(255, 255, 255, 0.2)' }}
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            onMouseEnter={(e) => {
              e.target.style.color = '#ffffff';
              e.target.style.borderColor = 'rgba(255, 255, 255, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.target.style.color = '#a3a3a3';
              e.target.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            }}
          >
            <svg className="fill-current h-3 w-3" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <title>Menu</title>
              <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z"/>
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3" style={{ borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className="block px-3 py-2 rounded-md text-base font-medium transition-colors"
                  style={{
                    color: isActive(item.path) ? '#33c2cc' : '#a3a3a3',
                    backgroundColor: isActive(item.path) ? 'rgba(51, 194, 204, 0.1)' : 'transparent'
                  }}
                  onClick={() => setIsMenuOpen(false)}
                  onMouseEnter={(e) => {
                    if (!isActive(item.path)) {
                      e.target.style.color = '#ffffff';
                      e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive(item.path)) {
                      e.target.style.color = '#a3a3a3';
                      e.target.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  {item.name}
                </Link>
              ))}
              <Link
                to="/contact"
                className="block px-3 py-2 mt-4 rounded-lg font-semibold text-center transition-colors"
                style={{ backgroundColor: '#33c2cc', color: '#030412' }}
                onClick={() => setIsMenuOpen(false)}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#57db96'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#33c2cc'}
              >
                Get Quote
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
